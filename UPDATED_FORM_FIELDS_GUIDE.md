# Updated Form Fields Guide - Work Order Management System

## Overview
The Customer Details and Asset Details sections have been updated to include only the specific fields you requested, maintaining the black and white theme design.

## Customer Details Section

### Fields Included:
1. **Mobile** - Text input for customer mobile number
2. **Name** - Text input for customer name
3. **Email** - Email input for customer email address
4. **Party Address** - Textarea for customer address
5. **PO #** - Text input for Purchase Order number
6. **Ship To** - Textarea for shipping address
7. **Is Drop Down In?** - Dropdown select with Yes/No options
8. **Contact Person** - Dropdown select with predefined contact options

### Field Details:

#### Mobile
- **Type**: Text input
- **ID**: `customerMobile`
- **Purpose**: Store customer's mobile phone number
- **Editable**: Yes

#### Name
- **Type**: Text input
- **ID**: `customerName`
- **Purpose**: Store customer's full name
- **Editable**: Yes

#### Email
- **Type**: Email input
- **ID**: `customerEmail`
- **Purpose**: Store customer's email address
- **Validation**: Email format validation
- **Editable**: Yes

#### Party Address
- **Type**: Textarea
- **ID**: `customerAddress`
- **Purpose**: Store customer's complete address
- **Editable**: Yes
- **Resizable**: Vertical only

#### PO #
- **Type**: Text input
- **ID**: `poNumber`
- **Purpose**: Store Purchase Order number
- **Editable**: Yes

#### Ship To
- **Type**: Textarea
- **ID**: `shipTo`
- **Purpose**: Store shipping address (can be different from party address)
- **Editable**: Yes
- **Resizable**: Vertical only

#### Is Drop Down In?
- **Type**: Dropdown select
- **ID**: `isDropDownIn`
- **Options**: 
  - Select Option (default)
  - Yes
  - No
- **Purpose**: Indicate if this is a drop-down service
- **Editable**: Yes

#### Contact Person
- **Type**: Dropdown select
- **ID**: `contactPerson`
- **Options**:
  - Select Contact (default)
  - Contact Person 1
  - Contact Person 2
  - Contact Person 3
- **Purpose**: Select primary contact person
- **Editable**: Yes

## Asset Details Section

### Fields Included:
1. **Unit #** - Text input for unit number
2. **Serial #** - Text input for serial number
3. **Model** - Text input for model (required field marked with *)
4. **Brand** - Text input for brand name
5. **Asset Type** - Dropdown select for asset type
6. **Key Tag #** - Text input for key tag number
7. **Odometer** - Number input for odometer reading
8. **Asset Status** - Dropdown select for asset status
9. **Is Breakdown** - Checkbox for breakdown indication
10. **Bay** - Dropdown select for bay location

### Field Details:

#### Unit #
- **Type**: Text input
- **ID**: `unitNumber`
- **Purpose**: Store unit identification number
- **Editable**: Yes

#### Serial #
- **Type**: Text input
- **ID**: `assetSerial`
- **Purpose**: Store asset serial number
- **Editable**: Yes

#### Model *
- **Type**: Text input
- **ID**: `assetModel`
- **Purpose**: Store asset model information
- **Required**: Yes (marked with red asterisk)
- **Validation**: Required field
- **Editable**: Yes

#### Brand
- **Type**: Text input
- **ID**: `assetBrand`
- **Purpose**: Store asset brand name
- **Editable**: Yes

#### Asset Type
- **Type**: Dropdown select
- **ID**: `assetType`
- **Options**:
  - Select Asset Type (default)
  - Coach
  - Truck
  - Van
  - Bus
  - Trailer
  - School Bus
- **Purpose**: Categorize the type of asset
- **Editable**: Yes

#### Key Tag #
- **Type**: Text input
- **ID**: `keyTag`
- **Purpose**: Store key tag identification number
- **Editable**: Yes

#### Odometer
- **Type**: Number input
- **ID**: `odometer`
- **Purpose**: Store current odometer reading
- **Placeholder**: "Enter odometer reading"
- **Validation**: Numeric values only
- **Editable**: Yes

#### Asset Status
- **Type**: Dropdown select
- **ID**: `assetStatus`
- **Options**:
  - Select Status (default)
  - Active
  - In Service
  - Available
  - Under Maintenance
  - Out of Service
- **Purpose**: Indicate current status of the asset
- **Editable**: Yes

#### Is Breakdown
- **Type**: Checkbox
- **ID**: `isBreakdown`
- **Label**: "Yes, this is a breakdown"
- **Purpose**: Indicate if the work order is for a breakdown
- **Styling**: Custom checkbox with label
- **Editable**: Yes

#### Bay
- **Type**: Dropdown select
- **ID**: `bay`
- **Options**:
  - Select Bay (default)
  - P53
  - Drive - Middle
  - Bay 1
  - Bay 2
  - Bay 3
  - Bay 4
  - Outdoor
- **Purpose**: Specify the bay location for the asset
- **Editable**: Yes

## Styling Features

### Black and White Theme Integration
- All form fields follow the black and white theme
- Consistent border styling and colors
- Proper contrast ratios for accessibility
- Focus indicators with black/white theme colors

### Form Validation
- Required fields marked with red asterisk (*)
- Email validation for email field
- Number validation for odometer field
- Dropdown validation for select fields

### Responsive Design
- Form fields adapt to different screen sizes
- Proper spacing and alignment
- Mobile-friendly input sizes
- Accessible touch targets

### Accessibility Features
- Proper label associations
- Keyboard navigation support
- Screen reader friendly
- High contrast design
- Clear focus indicators

## Data Integration

### JavaScript Integration
- All fields are properly connected to the data model
- Form population works with existing work order data
- Save functionality includes all new fields
- Validation handling for required fields

### Sample Data
- Updated sample data includes all new fields
- Realistic dropdown values
- Proper data types for each field
- Consistent data structure

## Usage Instructions

### Adding New Work Orders
1. Click "New Work Order" button
2. Fill in Customer Details tab with required information
3. Switch to Asset Details tab
4. Fill in asset information (Model field is required)
5. Use dropdowns for standardized values
6. Check "Is Breakdown" if applicable
7. Select appropriate bay location
8. Save the work order

### Editing Existing Work Orders
1. Click on any work order row to expand details
2. Click the work order number link to open modal
3. Edit fields as needed in Customer Details and Asset Details tabs
4. Save changes

### Field Validation
- Model field must be filled (required)
- Email field must contain valid email format
- Odometer must be numeric
- Dropdown fields should have valid selections

---

**Note**: All fields maintain the professional black and white theme design while providing comprehensive functionality for enterprise work order management.
