/* Google Material Design CSS Variables - White and Black Theme */
:root {
    /* Light Theme - Pure White and Black Material Design 3 */
    --md-sys-color-primary: #000000;
    --md-sys-color-on-primary: #ffffff;
    --md-sys-color-primary-container: #f5f5f5;
    --md-sys-color-on-primary-container: #000000;

    --md-sys-color-surface: #ffffff;
    --md-sys-color-surface-variant: #fafafa;
    --md-sys-color-surface-container: #f8f8f8;
    --md-sys-color-surface-container-high: #f0f0f0;
    --md-sys-color-on-surface: #000000;
    --md-sys-color-on-surface-variant: #424242;

    --md-sys-color-outline: #e0e0e0;
    --md-sys-color-outline-variant: #f5f5f5;

    --md-sys-color-success: #000000;
    --md-sys-color-warning: #424242;
    --md-sys-color-error: #000000;
    --md-sys-color-info: #000000;

    /* Elevation shadows */
    --md-sys-elevation-1: 0px 1px 3px rgba(0, 0, 0, 0.12), 0px 1px 2px rgba(0, 0, 0, 0.24);
    --md-sys-elevation-2: 0px 3px 6px rgba(0, 0, 0, 0.16), 0px 3px 6px rgba(0, 0, 0, 0.23);
    --md-sys-elevation-3: 0px 10px 20px rgba(0, 0, 0, 0.19), 0px 6px 6px rgba(0, 0, 0, 0.23);
    --md-sys-elevation-4: 0px 14px 28px rgba(0, 0, 0, 0.25), 0px 10px 10px rgba(0, 0, 0, 0.22);

    /* Typography */
    --md-sys-typescale-display-large: 57px;
    --md-sys-typescale-headline-large: 32px;
    --md-sys-typescale-headline-medium: 28px;
    --md-sys-typescale-title-large: 22px;
    --md-sys-typescale-title-medium: 16px;
    --md-sys-typescale-body-large: 16px;
    --md-sys-typescale-body-medium: 14px;
    --md-sys-typescale-label-large: 14px;
    --md-sys-typescale-label-medium: 12px;

    /* Border radius */
    --md-sys-shape-corner-none: 0px;
    --md-sys-shape-corner-extra-small: 4px;
    --md-sys-shape-corner-small: 8px;
    --md-sys-shape-corner-medium: 12px;
    --md-sys-shape-corner-large: 16px;
    --md-sys-shape-corner-extra-large: 28px;
}

[data-theme="dark"] {
    /* Dark Theme - Pure Black and White Material Design 3 */
    --md-sys-color-primary: #ffffff;
    --md-sys-color-on-primary: #000000;
    --md-sys-color-primary-container: #1a1a1a;
    --md-sys-color-on-primary-container: #ffffff;

    --md-sys-color-surface: #000000;
    --md-sys-color-surface-variant: #0a0a0a;
    --md-sys-color-surface-container: #1a1a1a;
    --md-sys-color-surface-container-high: #2a2a2a;
    --md-sys-color-on-surface: #ffffff;
    --md-sys-color-on-surface-variant: #cccccc;

    --md-sys-color-outline: #424242;
    --md-sys-color-outline-variant: #2a2a2a;

    --md-sys-color-success: #ffffff;
    --md-sys-color-warning: #cccccc;
    --md-sys-color-error: #ffffff;
    --md-sys-color-info: #ffffff;

    /* Elevation shadows for dark theme */
    --md-sys-elevation-1: 0px 1px 3px rgba(255, 255, 255, 0.1), 0px 1px 2px rgba(255, 255, 255, 0.08);
    --md-sys-elevation-2: 0px 3px 6px rgba(255, 255, 255, 0.12), 0px 3px 6px rgba(255, 255, 255, 0.1);
    --md-sys-elevation-3: 0px 10px 20px rgba(255, 255, 255, 0.15), 0px 6px 6px rgba(255, 255, 255, 0.12);
    --md-sys-elevation-4: 0px 14px 28px rgba(255, 255, 255, 0.18), 0px 10px 10px rgba(255, 255, 255, 0.15);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Google Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface);
    line-height: 1.5;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    font-size: var(--md-sys-typescale-body-medium);
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    background-color: var(--md-sys-color-surface);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    box-shadow: var(--md-sys-elevation-1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    max-width: 1400px;
    margin: 0 auto;
}

.header h1 {
    color: var(--md-sys-color-primary);
    font-size: var(--md-sys-typescale-title-large);
    font-weight: 500;
    letter-spacing: 0.15px;
}

.header h1 i {
    margin-right: 8px;
}

.header-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Material Design Button Styles */
.btn {
    padding: 10px 24px;
    border: none;
    border-radius: var(--md-sys-shape-corner-large);
    cursor: pointer;
    font-size: var(--md-sys-typescale-label-large);
    font-weight: 500;
    letter-spacing: 0.1px;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    min-height: 40px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: currentColor;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.btn:hover::before {
    opacity: 0.08;
}

.btn:focus::before {
    opacity: 0.12;
}

.btn:active::before {
    opacity: 0.16;
}

.btn-primary {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    box-shadow: var(--md-sys-elevation-1);
}

.btn-primary:hover {
    box-shadow: var(--md-sys-elevation-2);
}

.btn-secondary {
    background-color: var(--md-sys-color-surface-container);
    color: var(--md-sys-color-on-surface);
    border: 1px solid var(--md-sys-color-outline);
}

.btn-secondary:hover {
    background-color: var(--md-sys-color-surface-container-high);
}

.btn-sm {
    padding: 6px 16px;
    font-size: var(--md-sys-typescale-label-medium);
    min-height: 32px;
}

.theme-toggle {
    background: var(--md-sys-color-surface-container);
    border: 1px solid var(--md-sys-color-outline);
    color: var(--md-sys-color-on-surface);
    padding: 8px;
    border-radius: var(--md-sys-shape-corner-medium);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background-color: var(--md-sys-color-surface-container-high);
    box-shadow: var(--md-sys-elevation-1);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Filters Section */
.filters-section {
    background-color: var(--md-sys-color-surface);
    padding: 20px;
    border-radius: var(--md-sys-shape-corner-large);
    margin-bottom: 24px;
    box-shadow: var(--md-sys-elevation-1);
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: center;
    justify-content: space-between;
}

.search-container {
    position: relative;
    flex: 1;
    min-width: 280px;
}

.search-container i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--md-sys-color-on-surface-variant);
    font-size: 18px;
}

.search-container input {
    width: 100%;
    padding: 12px 16px 12px 48px;
    border: 1px solid var(--md-sys-color-outline);
    border-radius: var(--md-sys-shape-corner-large);
    background-color: var(--md-sys-color-surface-container);
    color: var(--md-sys-color-on-surface);
    font-size: var(--md-sys-typescale-body-large);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.search-container input:focus {
    outline: none;
    border-color: var(--md-sys-color-primary);
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.12);
    background-color: var(--md-sys-color-surface);
}

[data-theme="dark"] .search-container input:focus {
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.12);
}

.search-container input::placeholder {
    color: var(--md-sys-color-on-surface-variant);
}

.filter-tabs {
    display: flex;
    gap: 4px;
    background-color: var(--md-sys-color-surface-container);
    border-radius: var(--md-sys-shape-corner-large);
    padding: 4px;
}

.filter-tab {
    padding: 8px 16px;
    border: none;
    background-color: transparent;
    color: var(--md-sys-color-on-surface);
    border-radius: var(--md-sys-shape-corner-medium);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    font-size: var(--md-sys-typescale-label-large);
    font-weight: 500;
    position: relative;
}

.filter-tab.active {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    box-shadow: var(--md-sys-elevation-1);
}

.filter-tab:hover:not(.active) {
    background-color: var(--md-sys-color-surface-container-high);
}

.action-buttons {
    display: flex;
    gap: 8px;
}

/* Material Design Table Styles */
.table-container {
    background-color: var(--md-sys-color-surface);
    border-radius: var(--md-sys-shape-corner-large);
    overflow: hidden;
    box-shadow: var(--md-sys-elevation-1);
}

.work-orders-table {
    width: 100%;
    border-collapse: collapse;
    font-size: var(--md-sys-typescale-body-medium);
}

.work-orders-table th {
    background-color: var(--md-sys-color-surface-container);
    color: var(--md-sys-color-on-surface);
    padding: 16px 12px;
    text-align: left;
    font-weight: 500;
    font-size: var(--md-sys-typescale-label-large);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    position: sticky;
    top: 0;
    z-index: 10;
    letter-spacing: 0.1px;
}

.work-orders-table td {
    padding: 12px;
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    vertical-align: middle;
    background-color: var(--md-sys-color-surface);
}

.work-orders-table tbody tr {
    transition: background-color 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
}

.work-orders-table tbody tr:hover {
    background-color: var(--md-sys-color-surface-container);
}

.work-orders-table tbody tr.expanded {
    background-color: var(--md-sys-color-surface-container);
}

.work-orders-table tbody tr.expanded td {
    background-color: var(--md-sys-color-surface-container);
    border-bottom: none;
}

/* Improved expandable row styling */
.work-order-details {
    background-color: var(--md-sys-color-surface-variant);
    border-top: 1px solid var(--md-sys-color-outline-variant);
}

.work-order-details td {
    padding: 10 !important;
    background-color: var(--md-sys-color-surface-variant) !important;
}

/* Text-only Status Badges - No Button Styling */
.status-badge {
    padding: 0;
    border-radius: 0;
    font-size: var(--md-sys-typescale-body-medium);
    font-weight: 400;
    text-transform: none;
    letter-spacing: normal;
    display: inline;
    align-items: baseline;
    min-height: auto;
    background: none;
    border: none;
    color: var(--md-sys-color-on-surface);
}

/* Remove all status badge button styling - keep as plain text */

/* Text-only Priority Badges */
.priority-badge {
    padding: 0;
    border-radius: 0;
    font-size: var(--md-sys-typescale-body-medium);
    font-weight: 400;
    text-transform: none;
    letter-spacing: normal;
    background: none;
    border: none;
    color: var(--md-sys-color-on-surface);
    display: inline;
}

/* Text-only Mobile Rate Indicator */
.mobile-rate-indicator {
    padding: 0;
    border-radius: 0;
    font-size: var(--md-sys-typescale-body-medium);
    font-weight: 400;
    display: inline;
    align-items: baseline;
    min-height: auto;
    background: none;
    border: none;
    color: var(--md-sys-color-on-surface);
}

/* Expandable Row */
.expandable-icon {
    cursor: pointer;
    transition: transform 0.2s ease;
    color: var(--text-secondary);
}

.expandable-icon.expanded {
    transform: rotate(90deg);
}

.work-order-details {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
}

/* Material Design Expanded Work Order Header */
.expanded-work-order-header {
    padding: 16px 20px;
    background-color: var(--md-sys-color-surface-container);
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
}

.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Material Design Expanded Work Order Table */
.expanded-work-order-table {
    padding: 16px;
    overflow-x: auto;
    background-color: var(--md-sys-color-surface-container);
    max-width: 100%;
    border-radius: var(--md-sys-shape-corner-medium);
    box-shadow: var(--md-sys-elevation-1);
    position: relative;
    margin: 8px;
}

/* Remove scroll indicator for multi-row table */
.expanded-work-order-table:has(.multi-row)::after {
    display: none;
}

/* Enhanced Table-Based Expanded Work Order Details */
.expanded-details-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: var(--md-sys-color-surface);
    border-radius: var(--md-sys-shape-corner-medium);
    overflow: hidden;
    box-shadow: var(--md-sys-elevation-2);
    margin: 8px 0;
}

.expanded-details-table .details-row {
    border-bottom: 1px solid var(--md-sys-color-outline-variant);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.expanded-details-table .details-row:last-child {
    border-bottom: none;
}

.expanded-details-table .details-row:nth-child(odd) {
    background-color: var(--md-sys-color-surface);
}

.expanded-details-table .details-row:nth-child(even) {
    background-color: var(--md-sys-color-surface-variant);
}

.expanded-details-table .details-row:hover {
    background-color: var(--md-sys-color-surface-container) !important;
    transform: scale(1.001);
}

.field-label {
    font-size: var(--md-sys-typescale-label-medium);
    font-weight: 600;
    color: var(--md-sys-color-on-surface-variant);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 16px 20px;
    background-color: var(--md-sys-color-surface-container-high);
    border-right: 1px solid var(--md-sys-color-outline-variant);
    width: 16%;
    vertical-align: middle;
    text-align: left;
    white-space: nowrap;
}

.field-value {
    font-size: var(--md-sys-typescale-body-large);
    color: var(--md-sys-color-on-surface);
    padding: 16px 20px;
    vertical-align: middle;
    width: 17%;
    text-align: left;
    word-wrap: break-word;
    line-height: 1.4;
}

.field-value.work-order-cell {
    background-color: var(--md-sys-color-primary-container);
    border-radius: var(--md-sys-shape-corner-small);
    margin: 4px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.field-value.work-order-cell i {
    color: var(--md-sys-color-primary);
    font-size: 16px;
    flex-shrink: 0;
}

.field-value.work-order-cell .work-order-link {
    color: var(--md-sys-color-primary);
    font-weight: 600;
    text-decoration: none;
    font-size: var(--md-sys-typescale-title-medium);
    letter-spacing: 0.15px;
}

.field-value.work-order-cell .work-order-link:hover {
    text-decoration: underline;
}

/* Better spacing for badges and indicators */
.field-value .status-badge,
.field-value .priority-badge,
.field-value .mobile-rate-indicator {
    margin: 2px 0;
    display: inline-flex;
    align-items: center;
    min-height: 24px;
}

/* Legacy table styles - kept for compatibility */
.expanded-table tbody tr:hover {
    background-color: var(--md-sys-color-surface-container);
}

.expanded-table tbody tr:hover td {
    background-color: var(--md-sys-color-surface-container);
}

.expanded-table .work-order-link {
    color: var(--md-sys-color-primary);
    text-decoration: none;
    font-weight: 500;
    margin-left: 8px;
}

.expanded-table .work-order-link:hover {
    text-decoration: underline;
}

.expanded-table i.fas {
    color: var(--md-sys-color-on-surface-variant);
    font-size: 12px;
}

/* Legacy detail styles for backward compatibility */
.work-order-details-content {
    padding: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.detail-section h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.detail-item label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    color: var(--text-primary);
    font-weight: 500;
}

/* Enhanced Material Design Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 16px;
    backdrop-filter: blur(4px);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.modal-overlay.active {
    display: flex;
    animation: modalFadeIn 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.modal-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 85%;
    max-width: 1000px;
    max-height: 88vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transform: scale(0.9) translateY(20px);
    animation: modalSlideIn 0.3s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
    border: 1px solid #e0e0e0;
    position: relative;
}

/* Dark theme */
[data-theme="dark"] .modal-container {
    background: black;
    border: 1px solid #333;
    box-shadow: 0 4px 20px rgba(255, 255, 255, 0.1);
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.9) translateY(20px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

.modal-header {
    padding: 20px 32px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    min-height: 60px;
    position: relative;
}

/* Dark theme header */
[data-theme="dark"] .modal-header {
    background: black;
    border-bottom: 1px solid #333;
}

.modal-header h2 {
    color: black;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 0.5px;
    margin: 0;
    text-transform: uppercase;
}

/* Dark theme header text */
[data-theme="dark"] .modal-header h2 {
    color: white;
}

.modal-close {
    background: white;
    border: 1px solid #e0e0e0;
    font-size: 16px;
    color: black;
    cursor: pointer;
    padding: 0;
    border-radius: 4px;
    transition: all 0.2s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Dark theme close button */
[data-theme="dark"] .modal-close {
    background: black;
    border: 1px solid #333;
    color: white;
}

.modal-close:hover {
    background: #f5f5f5;
    border-color: #ccc;
}

[data-theme="dark"] .modal-close:hover {
    background: #222;
    border-color: #555;
}

/* Simplified Modal Content */
.modal-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: white;
    position: relative;
}

/* Dark theme modal content */
[data-theme="dark"] .modal-content {
    background: black;
}

/* Add subtle scroll indicator */
.modal-content::-webkit-scrollbar {
    width: 8px;
}

.modal-content::-webkit-scrollbar-track {
    background: var(--md-sys-color-surface-variant);
    border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb {
    background: var(--md-sys-color-outline);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.modal-content::-webkit-scrollbar-thumb:hover {
    background: var(--md-sys-color-primary);
}

.modal-footer {
    padding: 20px 32px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    background: white;
    min-height: 60px;
    align-items: center;
}

/* Dark theme footer */
[data-theme="dark"] .modal-footer {
    background: black;
    border-top: 1px solid #333;
}

/* Simplified Work Order Info Bar */
.work-order-info-bar {
    background: white;
    padding: 16px 32px;
    border-bottom: 1px solid #e0e0e0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px 24px;
}

/* Dark theme info bar */
[data-theme="dark"] .work-order-info-bar {
    background: black;
    border-bottom: 1px solid #333;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 0;
    background: none;
    border: none;
    position: relative;
}

.info-item label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

/* Dark theme info labels */
[data-theme="dark"] .info-item label {
    color: #999;
}

.info-item span {
    color: black;
    font-weight: 600;
    font-size: 14px;
    line-height: 1.4;
    padding-bottom: 8px;
    border-bottom: 1px solid #e0e0e0;
    position: relative;
}

/* Dark theme info values */
[data-theme="dark"] .info-item span {
    color: white;
    border-bottom: 1px solid #333;
}

/* Status badge styling */
.info-item span.status-badge {
    background: black;
    color: white;
    padding: 4px 12px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border: none;
    display: inline-block;
    width: fit-content;
}

/* Dark theme status badge */
[data-theme="dark"] .info-item span.status-badge {
    background: white;
    color: black;
}

/* Simplified Tab Navigation */
.tab-navigation {
    display: flex;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    overflow-x: auto;
    padding: 0 24px;
    gap: 0;
}

/* Dark theme tab navigation */
[data-theme="dark"] .tab-navigation {
    background: black;
    border-bottom: 1px solid #333;
}

.tab-btn {
    padding: 16px 24px;
    border: none;
    background: none;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    white-space: nowrap;
    position: relative;
    min-width: 160px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid transparent;
}

/* Dark theme tab buttons */
[data-theme="dark"] .tab-btn {
    color: #999;
}

.tab-btn.active {
    color: black;
    border-bottom: 2px solid black;
    font-weight: 600;
}

/* Dark theme active tab */
[data-theme="dark"] .tab-btn.active {
    color: white;
    border-bottom: 2px solid white;
}

.tab-btn:hover:not(.active) {
    color: black;
}

/* Dark theme tab hover */
[data-theme="dark"] .tab-btn:hover:not(.active) {
    color: white;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: currentColor;
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.tab-btn:hover::before {
    opacity: 0.08;
}

/* Material Design Tab Content */
.tab-content {
    padding: 24px;
    background-color: var(--md-sys-color-surface);
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Simplified Form Styles - 3 Column Layout */
.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px 20px;
    padding: 20px 32px;
    max-width: 100%;
    margin: 0 auto;
    background: white;
}

/* Dark theme form grid */
[data-theme="dark"] .form-grid {
    background: black;
}

/* Responsive layout for 3-column grid */
@media (max-width: 1200px) {
    .form-grid {
        grid-template-columns: 1fr 1fr;
        gap: 16px 20px;
    }
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 16px;
    }
}

/* Full-width fields for specific inputs */
.form-group.full-width {
    grid-column: 1 / -1;
}

/* Specific styling for address field to fit in row */
.form-group textarea[placeholder*="Address"],
.form-group textarea[placeholder*="address"] {
    min-height: 80px;
    max-height: 120px;
    resize: vertical;
    width: 100%;
    box-sizing: border-box;
}

/* Ensure Party Address field stays in its column */
.form-group:has(textarea[placeholder*="Address"]) {
    grid-column: span 1;
}

/* Planning grid specific styling */
.planning-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 20px;
}

.planning-section {
    background-color: var(--md-sys-color-surface-container);
    padding: 16px;
    border-radius: var(--md-sys-shape-corner-medium);
    border: 2px solid var(--md-sys-color-outline-variant);
}

.planning-section h4 {
    color: var(--md-sys-color-on-surface);
    margin-bottom: 16px;
    font-size: var(--md-sys-typescale-title-small);
    font-weight: 600;
    padding-bottom: 6px;
    border-bottom: 2px solid var(--md-sys-color-outline-variant);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
    position: relative;
}

.form-group label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    letter-spacing: 0.3px;
    margin-bottom: 4px;
    text-transform: none;
    display: block;
}

/* Dark theme form labels */
[data-theme="dark"] .form-group label {
    color: #999;
}

/* Simplified input styling with underline */
.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 0;
    border: none;
    border-bottom: 1px solid #e0e0e0;
    border-radius: 0;
    background: transparent;
    color: black;
    font-size: 14px;
    font-family: inherit;
    transition: all 0.2s ease;
    min-height: auto;
    box-sizing: border-box;
    width: 100%;
    outline: none;
}

/* Dark theme inputs */
[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group select,
[data-theme="dark"] .form-group textarea {
    color: white;
    border-bottom: 1px solid #333;
}

/* Simplified focus states */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-bottom: 2px solid black;
}

/* Dark theme focus states */
[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group select:focus,
[data-theme="dark"] .form-group textarea:focus {
    border-bottom: 2px solid white;
}

/* Simplified Textarea specific styling */
.form-group textarea {
    resize: vertical;
    min-height: 60px;
    max-height: 100px;
    line-height: 1.4;
    padding: 8px 0;
    font-family: inherit;
    overflow-y: auto;
}

/* Select dropdown styling */
.form-group select {
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 20px;
    padding-right: 48px;
}

/* Number input styling */
.form-group input[type="number"] {
    appearance: textfield;
    -moz-appearance: textfield;
}

.form-group input[type="number"]::-webkit-outer-spin-button,
.form-group input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.form-group input[readonly] {
    background-color: var(--md-sys-color-surface-variant);
    color: var(--md-sys-color-on-surface-variant);
    border-color: var(--md-sys-color-outline-variant);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.5;
}

/* Enhanced Checkbox Container Styling */
.checkbox-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background-color: var(--md-sys-color-surface-container);
    border: 2px solid var(--md-sys-color-outline);
    border-radius: var(--md-sys-shape-corner-medium);
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
    cursor: pointer;
    min-height: 56px;
    box-sizing: border-box;
}

.checkbox-container:hover {
    border-color: var(--md-sys-color-primary);
    background-color: var(--md-sys-color-surface-container-high);
    transform: translateY(-1px);
}

.checkbox-container input[type="checkbox"] {
    width: 20px;
    height: 20px;
    margin: 0;
    cursor: pointer;
    accent-color: var(--md-sys-color-primary);
    transform: scale(1.2);
}

.checkbox-label {
    font-size: var(--md-sys-typescale-body-large);
    color: var(--md-sys-color-on-surface);
    cursor: pointer;
    user-select: none;
    font-weight: 500;
    flex: 1;
}

/* Required field indicator */
.form-group label[for="assetModel"]::after,
.form-group label:has(+ input[required])::after {
    content: " *";
    color: var(--md-sys-color-error);
    font-weight: bold;
}

/* Asset Details Specific Styles */
.asset-warranty-section h3 {
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.warranty-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.warranty-column h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1rem;
    font-weight: 600;
}

.asset-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}



/* Enhanced Job Details Specific Styles */
.job-details-section {
    padding: 16px;
}

.status-badges-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
    padding: 16px;
    background-color: var(--md-sys-color-surface-container);
    border-radius: var(--md-sys-shape-corner-large);
    border: 2px solid var(--md-sys-color-outline-variant);
}

.status-badges-container::before {
    content: "Job Status Legend";
    width: 100%;
    font-size: var(--md-sys-typescale-title-medium);
    font-weight: 600;
    color: var(--md-sys-color-on-surface);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 2px solid var(--md-sys-color-outline-variant);
}

.status-badges-container .status-badge {
    font-size: var(--md-sys-typescale-label-medium);
    padding: 8px 16px;
    min-height: 32px;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.status-badges-container .status-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--md-sys-elevation-2);
}

/* Text-only status indicators - no button styling */
.expanded-details-table .status-badge,
.expanded-details-table .priority-badge,
.expanded-details-table .mobile-rate-indicator {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    font-size: var(--md-sys-typescale-body-medium);
    color: var(--md-sys-color-on-surface);
    font-weight: 400;
    text-decoration: none;
    display: inline;
    min-height: auto;
    border-radius: 0;
    box-shadow: none;
}

/* Keep button styling only for job details legend */
.status-badges-container .status-badge.job-completed {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    border: 2px solid var(--md-sys-color-primary);
    text-decoration: underline;
}

.status-badges-container .status-badge.assign-to-parts {
    background-color: var(--md-sys-color-surface-container);
    color: var(--md-sys-color-on-surface);
    border: 1px solid var(--md-sys-color-outline);
    font-style: italic;
}

.status-badges-container .status-badge.billed {
    background-color: var(--md-sys-color-primary);
    color: var(--md-sys-color-on-primary);
    border: 2px solid var(--md-sys-color-primary);
    font-weight: 600;
}

.status-badges-container .status-badge.closed {
    background-color: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface-variant);
    border: 1px solid var(--md-sys-color-outline);
    text-decoration: line-through;
}

/* Enhanced Job Form Grid - 3 Column Layout */
.job-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 12px 20px;
    padding: 20px 32px;
    background: white;
}

/* Dark theme job form grid */
[data-theme="dark"] .job-form-grid {
    background: black;
}

/* Full width for specific job detail fields */
.job-form-grid .form-group:first-child {
    grid-column: 1 / -1;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .header-content {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .filters-section {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-tabs {
        justify-content: center;
    }

    .action-buttons {
        justify-content: center;
    }

    .table-container {
        overflow-x: auto;
    }

    .work-orders-table {
        min-width: 800px;
    }

    .modal-container {
        width: 95%;
        max-width: 95vw;
        margin: 0.5rem;
        max-height: 92vh;
        border-radius: 20px;
    }

    .modal-header,
    .modal-footer {
        padding: 1rem;
    }

    .tab-content {
        padding: 1rem;
    }

    /* Mobile form adjustments */
    .form-grid,
    .job-form-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        padding: 16px;
    }

    .work-order-info-bar {
        grid-template-columns: 1fr;
        padding: 1rem;
    }

    .tab-navigation {
        overflow-x: auto;
    }

    .tab-btn {
        white-space: nowrap;
        min-width: 150px;
    }

    /* Mobile checkbox adjustments */
    .checkbox-container {
        padding: 12px 16px;
        min-height: 48px;
    }

    /* Mobile status badges */
    .status-badges-container {
        padding: 16px;
        gap: 8px;
    }

    .status-badges-container .status-badge {
        font-size: var(--md-sys-typescale-label-small);
        padding: 6px 12px;
        min-height: 28px;
    }

    /* Mobile responsive for table-based expanded details */
    .expanded-work-order-table {
        padding: 12px;
        margin: 4px;
    }

    .expanded-details-table {
        font-size: var(--md-sys-typescale-body-small);
        margin: 4px 0;
    }

    .field-label {
        font-size: var(--md-sys-typescale-label-small);
        padding: 12px 16px;
        width: 18%;
    }

    .field-value {
        font-size: var(--md-sys-typescale-body-medium);
        padding: 12px 16px;
        width: 15%;
    }

    .field-value.work-order-cell {
        padding: 8px 12px;
        margin: 2px;
    }

    .field-value.work-order-cell .work-order-link {
        font-size: var(--md-sys-typescale-title-small);
    }

    /* Better mobile modal */
    .modal-container {
        margin: 8px;
        max-height: 98vh;
        border-radius: var(--md-sys-shape-corner-large);
    }

    .modal-header {
        padding: 16px 20px;
        min-height: 56px;
    }

    .modal-header h2 {
        font-size: var(--md-sys-typescale-title-large);
    }

    .modal-footer {
        padding: 16px 20px;
        min-height: 64px;
        flex-wrap: wrap;
        gap: 8px;
    }

    .btn {
        min-width: 120px;
    }
}

/* Stack table on very small screens */
@media (max-width: 480px) {
    .expanded-details-table,
    .expanded-details-table tbody,
    .expanded-details-table tr,
    .expanded-details-table td {
        display: block;
        width: 100%;
    }

    .expanded-details-table tr {
        border: 1px solid var(--md-sys-color-outline-variant);
        margin-bottom: 8px;
        border-radius: var(--md-sys-shape-corner-small);
    }

    .field-label {
        background-color: var(--md-sys-color-primary-container);
        color: var(--md-sys-color-on-primary-container);
        font-weight: 600;
        border-right: none;
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .field-value {
        border-bottom: 1px solid var(--md-sys-color-outline-variant);
    }

    .field-value:last-child {
        border-bottom: none;
    }
}

/* Enhanced Material Design Scrollbar Styling */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: var(--md-sys-color-surface-variant);
    border-radius: var(--md-sys-shape-corner-small);
}

::-webkit-scrollbar-thumb {
    background: var(--md-sys-color-outline);
    border-radius: var(--md-sys-shape-corner-small);
    border: 2px solid var(--md-sys-color-surface-variant);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--md-sys-color-on-surface-variant);
}

::-webkit-scrollbar-corner {
    background: var(--md-sys-color-surface-variant);
}

/* Custom scrollbar for expanded table */
.expanded-work-order-table::-webkit-scrollbar {
    height: 8px;
}

.expanded-work-order-table::-webkit-scrollbar-track {
    background: var(--md-sys-color-surface-container);
    border-radius: var(--md-sys-shape-corner-small);
}

.expanded-work-order-table::-webkit-scrollbar-thumb {
    background: var(--md-sys-color-primary);
    border-radius: var(--md-sys-shape-corner-small);
    border: 1px solid var(--md-sys-color-surface-container);
}

.expanded-work-order-table::-webkit-scrollbar-thumb:hover {
    background: var(--md-sys-color-on-primary-container);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--accent-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Enhanced Black and White Theme Patterns */
.status-badge.scheduled::before {
    content: "⏰ ";
    font-size: 0.8em;
}

.status-badge.in-progress::before {
    content: "⚡ ";
    font-size: 0.8em;
}

.status-badge.completed::before {
    content: "✓ ";
    font-size: 0.8em;
}

.status-badge.cancelled::before {
    content: "✗ ";
    font-size: 0.8em;
}

.status-badge.pending::before {
    content: "⏳ ";
    font-size: 0.8em;
}

.priority-badge.priority-critical::before {
    content: "🔥 ";
    font-size: 0.8em;
}

.priority-badge.priority-high::before {
    content: "⚠️ ";
    font-size: 0.8em;
}

.priority-badge.priority-medium::before {
    content: "📋 ";
    font-size: 0.8em;
}

.priority-badge.priority-low::before {
    content: "📝 ";
    font-size: 0.8em;
}

.mobile-rate-indicator.yes::before {
    content: "📱 ";
    font-size: 0.8em;
}

.mobile-rate-indicator.no::before {
    content: "🏢 ";
    font-size: 0.8em;
}

/* Animations */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Enhanced hover effects for black and white theme */
.work-orders-table tbody tr:hover {
    background-color: var(--md-sys-color-surface-container);
    transform: scale(1.001);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.btn:hover {
    transform: translateY(-1px);
    transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
}

/* Enhanced focus indicators for accessibility */
.btn:focus-visible,
.filter-tab:focus-visible,
.tab-btn:focus-visible {
    outline: 2px solid var(--md-sys-color-primary);
    outline-offset: 2px;
}

/* Reduced button sizes for compact layout */
.modal-footer .btn {
    padding: 8px 16px;
    min-height: 36px;
    font-size: var(--md-sys-typescale-label-large);
    min-width: 100px;
}

.status-badge {
    padding: 4px 12px;
    min-height: 28px;
    font-size: var(--md-sys-typescale-label-medium);
}

/* Reduced form field heights */
.form-group input,
.form-group select {
    min-height: 40px;
    padding: 8px 12px;
    font-size: var(--md-sys-typescale-body-medium);
}

.form-group label {
    margin-bottom: 4px;
    font-size: var(--md-sys-typescale-body-small);
}

/* Reduced spacing between form groups */
.form-group {
    margin-bottom: 12px;
}

.form-grid {
    gap: 8px 16px;
    padding: 16px 24px;
}

.job-form-grid {
    gap: 8px 16px;
    padding: 16px 24px;
}

/* Print styles for black and white theme */
@media print {
    * {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
    }

    .status-badge,
    .priority-badge,
    .mobile-rate-indicator {
        border: 1px solid black !important;
        background: white !important;
        color: black !important;
    }
}
