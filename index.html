<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Work Order Management System</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Google+Sans:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <h1><i class="fas fa-clipboard-list"></i> Work Order Management</h1>
                <div class="header-controls">
                    <button class="btn btn-primary" id="newWorkOrderBtn">
                        <i class="fas fa-plus"></i> New Work Order
                    </button>
                    <button class="theme-toggle" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Filters and Search -->
            <div class="filters-section">
                <div class="search-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="searchInput" placeholder="Search work orders...">
                </div>
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">All Queue</button>
                    <button class="filter-tab" data-filter="my">My Queue</button>
                    <button class="filter-tab" data-filter="group">Group Queue</button>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-secondary">
                        <i class="fas fa-expand-arrows-alt"></i> Expand All
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-compress-arrows-alt"></i> Collapse All
                    </button>
                    <button class="btn btn-secondary">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>

            <!-- Work Orders Table -->
            <div class="table-container">
                <table class="work-orders-table" id="workOrdersTable">
                    <thead>
                        <tr>
                            <th></th>
                            <th>Work Order #</th>
                            <th>Customer Account #</th>
                            <th>Customer Name</th>
                            <th>Serial #</th>
                            <th>Quotation #</th>
                            <th>Ticket #</th>
                            <th>Planned Start Date</th>
                            <th>Planned Completion Date</th>
                            <th>Unit #</th>
                            <th>Status</th>
                            <th>Key Tag #</th>
                            <th>Bay</th>
                        </tr>
                    </thead>
                    <tbody id="workOrdersTableBody">
                        <!-- Work orders will be populated here -->
                    </tbody>
                </table>
            </div>
        </main>

        <!-- Work Order Detail Modal -->
        <div class="modal-overlay" id="workOrderModal">
            <div class="modal-container">
                <div class="modal-header">
                    <h2 id="modalTitle">Work Order Details</h2>
                    <button class="modal-close" id="closeModal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="modal-content">
                    <!-- Work Order Info Bar -->
                    <div class="work-order-info-bar">
                        <div class="info-item">
                            <label>Work Order #:</label>
                            <span id="modalWorkOrderNumber"></span>
                        </div>
                        <div class="info-item">
                            <label>Work Order Status:</label>
                            <span id="modalWorkOrderStatus" class="status-badge"></span>
                        </div>
                        <div class="info-item">
                            <label>Work Order Date:</label>
                            <span id="modalWorkOrderDate"></span>
                        </div>
                        <div class="info-item">
                            <label>Quotation #:</label>
                            <span id="modalQuotationNumber"></span>
                        </div>
                        <div class="info-item">
                            <label>Ticket #:</label>
                            <span id="modalTicketNumber"></span>
                        </div>
                        <div class="info-item">
                            <label>Quotation Date:</label>
                            <span id="modalQuotationDate"></span>
                        </div>
                    </div>

                    <!-- Tab Navigation -->
                    <div class="tab-navigation">
                        <button class="tab-btn active" data-tab="customer">Customer Details</button>
                        <button class="tab-btn" data-tab="asset">Asset Details</button>
                        <button class="tab-btn" data-tab="planning">Planning And Schedule</button>
                        <button class="tab-btn" data-tab="job">Job Details</button>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content">
                        <!-- Customer Details Tab -->
                        <div class="tab-pane active" id="customerTab">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Mobile</label>
                                    <input type="text" id="customerMobile" placeholder="Enter mobile number">
                                </div>
                                <div class="form-group">
                                    <label>Name</label>
                                    <input type="text" id="customerName" placeholder="Enter customer name">
                                </div>
                                <div class="form-group">
                                    <label>Email</label>
                                    <input type="email" id="customerEmail" placeholder="Enter email address">
                                </div>
                                <div class="form-group full-width">
                                    <label>Party Address</label>
                                    <textarea id="customerAddress" placeholder="Enter complete party address"></textarea>
                                </div>
                                <div class="form-group">
                                    <label>PO #</label>
                                    <input type="text" id="poNumber" placeholder="Enter Purchase Order number">
                                </div>
                                <div class="form-group">
                                    <label>Is Drop Down In?</label>
                                    <select id="isDropDownIn">
                                        <option value="">Select Option</option>
                                        <option value="yes">Yes</option>
                                        <option value="no">No</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Contact Person</label>
                                    <select id="contactPerson">
                                        <option value="">Select Contact</option>
                                        <option value="contact1">Contact Person 1</option>
                                        <option value="contact2">Contact Person 2</option>
                                        <option value="contact3">Contact Person 3</option>
                                    </select>
                                </div>
                                <div class="form-group full-width">
                                    <label>Ship To</label>
                                    <textarea id="shipTo" placeholder="Enter shipping address"></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Asset Details Tab -->
                        <div class="tab-pane" id="assetTab">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Unit #</label>
                                    <input type="text" id="unitNumber" placeholder="Enter unit number">
                                </div>
                                <div class="form-group">
                                    <label>Serial #</label>
                                    <input type="text" id="assetSerial" placeholder="Enter serial number">
                                </div>
                                <div class="form-group">
                                    <label>Model *</label>
                                    <input type="text" id="assetModel" required placeholder="Enter model (required)">
                                </div>
                                <div class="form-group">
                                    <label>Brand</label>
                                    <input type="text" id="assetBrand" placeholder="Enter brand name">
                                </div>
                                <div class="form-group">
                                    <label>Asset Type</label>
                                    <select id="assetType">
                                        <option value="">Select Asset Type</option>
                                        <option value="coach">Coach</option>
                                        <option value="truck">Truck</option>
                                        <option value="van">Van</option>
                                        <option value="bus">Bus</option>
                                        <option value="trailer">Trailer</option>
                                        <option value="school-bus">School Bus</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Key Tag #</label>
                                    <input type="text" id="keyTag" placeholder="Enter key tag number">
                                </div>
                                <div class="form-group">
                                    <label>Odometer</label>
                                    <input type="number" id="odometer" placeholder="Enter odometer reading">
                                </div>
                                <div class="form-group">
                                    <label>Asset Status</label>
                                    <select id="assetStatus">
                                        <option value="">Select Status</option>
                                        <option value="active">Active</option>
                                        <option value="in-service">In Service</option>
                                        <option value="available">Available</option>
                                        <option value="maintenance">Under Maintenance</option>
                                        <option value="out-of-service">Out of Service</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Is Breakdown</label>
                                    <div class="checkbox-container">
                                        <input type="checkbox" id="isBreakdown">
                                        <span class="checkbox-label">Yes, this is a breakdown</span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label>Bay</label>
                                    <select id="bay">
                                        <option value="">Select Bay</option>
                                        <option value="p53">P53</option>
                                        <option value="drive-middle">Drive - Middle</option>
                                        <option value="bay-1">Bay 1</option>
                                        <option value="bay-2">Bay 2</option>
                                        <option value="bay-3">Bay 3</option>
                                        <option value="bay-4">Bay 4</option>
                                        <option value="outdoor">Outdoor</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Planning And Schedule Tab -->
                        <div class="tab-pane" id="planningTab">
                            <div class="form-grid">
                                <div class="form-group">
                                    <label>Expected Arrival Date And Time</label>
                                    <input type="datetime-local" id="expectedArrival">
                                </div>
                                <div class="form-group">
                                    <label>Actual Arrival Date And Time</label>
                                    <input type="datetime-local" id="actualArrival">
                                </div>
                                <div class="form-group">
                                    <label>Expected Delivery Date And Time</label>
                                    <input type="datetime-local" id="expectedDelivery">
                                </div>
                                <div class="form-group">
                                    <label>Actual Delivery Date And Time</label>
                                    <input type="datetime-local" id="actualDelivery">
                                </div>
                                <div class="form-group">
                                    <label>Planned Start Date</label>
                                    <input type="date" id="plannedStartDate">
                                </div>
                                <div class="form-group">
                                    <label>Planned End Date</label>
                                    <input type="date" id="plannedEndDate">
                                </div>
                                <div class="form-group">
                                    <label>Actual Start Date</label>
                                    <input type="date" id="actualStartDate">
                                </div>
                                <div class="form-group">
                                    <label>Actual End Date</label>
                                    <input type="date" id="actualEndDate">
                                </div>
                            </div>
                        </div>

                        <!-- Job Details Tab -->
                        <div class="tab-pane" id="jobTab">
                            <div class="job-details-section">
                                <div class="status-badges-container">
                                    <span class="status-badge created">Created</span>
                                    <span class="status-badge moved-to-tech">Moved To Tech</span>
                                    <span class="status-badge in-progress">In Progress</span>
                                    <span class="status-badge job-completed">Job Completed By Tech</span>
                                    <span class="status-badge assign-to-parts">Assign To Parts Specialist</span>
                                    <span class="status-badge completed">Completed</span>
                                    <span class="status-badge billed">Billed</span>
                                    <span class="status-badge closed">Closed</span>
                                    <span class="status-badge cancelled">Cancelled</span>
                                    <span class="status-badge pending">Pending For Quote Approval</span>
                                </div>

                                <div class="job-form-grid">
                                    <div class="form-group full-width">
                                        <label>Job Card Status Legend</label>
                                        <input type="text" id="jobCardStatus" readonly placeholder="Current job status">
                                    </div>
                                    <div class="form-group">
                                        <label>Customer Complaint</label>
                                        <textarea id="customerComplaint" placeholder="Describe the customer's complaint or issue..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>Cause Of Failure</label>
                                        <textarea id="causeOfFailure" placeholder="Identify the root cause of the failure..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>Corrective Action</label>
                                        <textarea id="correctiveAction" placeholder="Describe the corrective action taken..."></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label>Action For Next Service</label>
                                        <textarea id="actionForNextService" placeholder="Recommendations for next service..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button class="btn btn-secondary" id="cancelBtn">Cancel</button>
                    <button class="btn btn-primary" id="saveBtn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <script src="data.js"></script>
    <script src="script.js"></script>
</body>
</html>
