// Sample Work Order Data
const workOrdersData = [
    {
        id: 1,
        workOrderNumber: "WO177N22025",
        customerAccount: "*********",
        customerName: "Customer 11667",
        serialNumber: "Serial998",
        quotationNumber: "QO177N22025",
        ticketNumber: "",
        plannedStartDate: "2024-03-24",
        plannedCompletionDate: "2024-03-25",
        unitNumber: "",
        status: "Drop In",
        keyTag: "",
        bay: "",
        workOrderDate: "2024-03-24",
        quotationDate: "2024-03-24",
        quotationStatus: "Approved",
        assignedTo: "Admin",
        finYear: "2024-25",
        date: "2024-03-24",
        model: "Model 36",
        unitNumber: "",
        ticketNumber: "",
        serviceType: "Repair",
        workPriority: "High",
        isMobileRate: false,
        customerDetails: {
            mobile: "************",
            email: "<EMAIL>",
            address: "Address 11667",
            contactPerson: "contact1",
            poNumber: "",
            shipTo: "",
            isDropDownIn: "yes"
        },
        assetDetails: {
            model: "Model 36",
            assetType: "coach",
            odometer: "11",
            brand: "Brand Name",
            assetStatus: "active",
            isBreakdown: false
        },
        planningDetails: {
            expectedArrival: "2024-03-24T12:00",
            actualArrival: "2024-03-24T12:00",
            expectedDelivery: "",
            actualDelivery: "",
            actualStartDate: "",
            actualEndDate: ""
        },
        jobDetails: {
            customerComplaint: "Axle replacement",
            causeOfFailure: "",
            correctiveAction: "",
            actionForNextService: "",
            jobCardStatus: "Moved to Tech"
        }
    },
    {
        id: 2,
        workOrderNumber: "WO177N21024",
        customerAccount: "*********",
        customerName: "Customer 13231",
        serialNumber: "Serial2154",
        quotationNumber: "QO177N15142023",
        ticketNumber: "",
        plannedStartDate: "2024-12-17",
        plannedCompletionDate: "2024-12-18",
        unitNumber: "",
        status: "Scheduled",
        keyTag: "",
        bay: "",
        workOrderDate: "2024-12-17",
        quotationDate: "2024-12-17",
        quotationStatus: "Approved",
        assignedTo: "Tech Team",
        finYear: "2024-25",
        date: "2024-12-17",
        model: "Model 42",
        unitNumber: "",
        ticketNumber: "",
        serviceType: "Maintenance",
        workPriority: "Medium",
        isMobileRate: true,
        customerDetails: {
            mobile: "************",
            email: "<EMAIL>",
            address: "Address 13231",
            contactPerson: "contact2",
            poNumber: "PO123456",
            shipTo: "Ship Address",
            isDropDownIn: "no"
        },
        assetDetails: {
            model: "Model 42",
            assetType: "truck",
            odometer: "25000",
            brand: "Brand ABC",
            assetStatus: "in-service",
            isBreakdown: false
        },
        planningDetails: {
            expectedArrival: "2024-12-17T09:00",
            actualArrival: "",
            expectedDelivery: "2024-12-18T17:00",
            actualDelivery: "",
            actualStartDate: "",
            actualEndDate: ""
        },
        jobDetails: {
            customerComplaint: "Regular maintenance check",
            causeOfFailure: "",
            correctiveAction: "",
            actionForNextService: "",
            jobCardStatus: "Scheduled"
        }
    },
    {
        id: 3,
        workOrderNumber: "WO177N19024",
        customerAccount: "*********",
        customerName: "Customer 12860",
        serialNumber: "Serial3672",
        quotationNumber: "QO177N10122023",
        ticketNumber: "",
        plannedStartDate: "2023-09-01",
        plannedCompletionDate: "2023-09-01",
        unitNumber: "6512",
        status: "Drop In",
        keyTag: "",
        bay: "",
        workOrderDate: "2023-09-01",
        quotationDate: "2023-09-01",
        quotationStatus: "Approved",
        assignedTo: "John Smith",
        finYear: "2023-24",
        date: "2023-09-01",
        model: "Model 55",
        unitNumber: "6512",
        ticketNumber: "",
        serviceType: "Emergency Repair",
        workPriority: "Critical",
        isMobileRate: false,
        customerDetails: {
            mobile: "************",
            email: "<EMAIL>",
            address: "Address 12860",
            contactPerson: "Contact 12860",
            poNumber: "PO789012",
            shipTo: "Ship Address 3"
        },
        assetDetails: {
            model: "Model 55",
            assetType: "Van",
            odometer: "15000",
            brand: "Brand DEF",
            brand2: "Brand UVW",
            serialStat: "Active",
            assetStatus: "In Service",
            maintenance: "Corrective",
            key: "Key 3",
            isBreakdown: true
        },
        planningDetails: {
            expectedArrival: "2023-09-01T08:00",
            actualArrival: "2023-09-01T08:15",
            expectedDelivery: "2023-09-01T16:00",
            actualDelivery: "2023-09-01T15:45",
            actualStartDate: "2023-09-01",
            actualEndDate: "2023-09-01"
        },
        jobDetails: {
            customerComplaint: "Engine making unusual noise",
            causeOfFailure: "Worn belt tensioner",
            correctiveAction: "Replaced belt tensioner and drive belt",
            actionForNextService: "Check belt tension in 6 months",
            jobCardStatus: "Completed"
        }
    },
    {
        id: 4,
        workOrderNumber: "WO177N17024",
        customerAccount: "*********",
        customerName: "Customer 10",
        serialNumber: "Serial9999",
        quotationNumber: "",
        ticketNumber: "",
        plannedStartDate: "2024-09-04",
        plannedCompletionDate: "2024-09-12",
        unitNumber: "38-098",
        status: "Drop In",
        keyTag: "",
        bay: "P53",
        workOrderDate: "2024-09-04",
        quotationDate: "",
        quotationStatus: "Pending",
        assignedTo: "Mike Johnson",
        finYear: "2024-25",
        date: "2024-09-04",
        model: "Model 78",
        unitNumber: "38-098",
        ticketNumber: "",
        serviceType: "Overhaul",
        workPriority: "High",
        isMobileRate: true,
        customerDetails: {
            mobile: "************",
            email: "<EMAIL>",
            address: "Address 10",
            contactPerson: "Contact 10",
            poNumber: "",
            shipTo: ""
        },
        assetDetails: {
            model: "Model 78",
            assetType: "Bus",
            odometer: "45000",
            brand: "Brand GHI",
            brand2: "Brand RST",
            serialStat: "Active",
            assetStatus: "In Service",
            maintenance: "Emergency",
            key: "Key 4",
            isBreakdown: true
        },
        planningDetails: {
            expectedArrival: "2024-09-04T10:00",
            actualArrival: "2024-09-04T10:30",
            expectedDelivery: "2024-09-12T14:00",
            actualDelivery: "",
            actualStartDate: "2024-09-04",
            actualEndDate: ""
        },
        jobDetails: {
            customerComplaint: "Transmission slipping",
            causeOfFailure: "Low transmission fluid and worn clutch",
            correctiveAction: "Transmission rebuild in progress",
            actionForNextService: "Regular transmission service every 30,000 miles",
            jobCardStatus: "In Progress"
        }
    },
    {
        id: 5,
        workOrderNumber: "WO177N15024",
        customerAccount: "*********",
        customerName: "Customer 12406",
        serialNumber: "Serial8940",
        quotationNumber: "QO177N26024",
        ticketNumber: "TI177N28024",
        plannedStartDate: "2024-01-21",
        plannedCompletionDate: "2024-01-21",
        unitNumber: "WOLF BUS",
        status: "Drop In",
        keyTag: "",
        bay: "Drive - Middle",
        workOrderDate: "2024-01-21",
        quotationDate: "2024-01-21",
        quotationStatus: "Approved",
        assignedTo: "Sarah Wilson",
        finYear: "2024-25",
        date: "2024-01-21",
        model: "Wolf Bus Model",
        unitNumber: "WOLF BUS",
        ticketNumber: "TI177N28024",
        serviceType: "Inspection",
        workPriority: "Low",
        isMobileRate: false,
        customerDetails: {
            mobile: "************",
            email: "<EMAIL>",
            address: "Address 12406",
            contactPerson: "Contact 12406",
            poNumber: "PO345678",
            shipTo: "Ship Address 5"
        },
        assetDetails: {
            model: "Wolf Bus Model",
            assetType: "School Bus",
            odometer: "32000",
            brand: "Wolf",
            brand2: "Educational Transport",
            serialStat: "Active",
            assetStatus: "In Service",
            maintenance: "Scheduled",
            key: "Key 5",
            isBreakdown: false
        },
        planningDetails: {
            expectedArrival: "2024-01-21T07:00",
            actualArrival: "2024-01-21T07:15",
            expectedDelivery: "2024-01-21T15:00",
            actualDelivery: "",
            actualStartDate: "2024-01-21",
            actualEndDate: ""
        },
        jobDetails: {
            customerComplaint: "Annual safety inspection",
            causeOfFailure: "",
            correctiveAction: "Safety inspection completed, minor brake adjustment",
            actionForNextService: "Next annual inspection due in 12 months",
            jobCardStatus: "Pending For Quote Approval"
        }
    },
    {
        id: 6,
        workOrderNumber: "WO177N11024",
        customerAccount: "*********",
        customerName: "Customer 10",
        serialNumber: "Serial9999",
        quotationNumber: "",
        ticketNumber: "",
        plannedStartDate: "",
        plannedCompletionDate: "",
        unitNumber: "",
        status: "Drop In",
        keyTag: "",
        bay: "P53",
        workOrderDate: "2024-03-15",
        quotationDate: "",
        quotationStatus: "Draft",
        assignedTo: "Unassigned",
        finYear: "2024-25",
        date: "2024-03-15",
        model: "Model 90",
        unitNumber: "",
        ticketNumber: "",
        serviceType: "Routine",
        workPriority: "Low",
        isMobileRate: false,
        customerDetails: {
            mobile: "************",
            email: "<EMAIL>",
            address: "Address 10",
            contactPerson: "Contact 10",
            poNumber: "",
            shipTo: ""
        },
        assetDetails: {
            model: "Model 90",
            assetType: "Trailer",
            odometer: "8000",
            brand: "Brand JKL",
            brand2: "Brand NOP",
            serialStat: "Active",
            assetStatus: "Available",
            maintenance: "Routine",
            key: "Key 6",
            isBreakdown: false
        },
        planningDetails: {
            expectedArrival: "",
            actualArrival: "",
            expectedDelivery: "",
            actualDelivery: "",
            actualStartDate: "",
            actualEndDate: ""
        },
        jobDetails: {
            customerComplaint: "Tire replacement needed",
            causeOfFailure: "",
            correctiveAction: "",
            actionForNextService: "",
            jobCardStatus: "Created"
        }
    }
];

// Status color mapping
const statusColors = {
    "Drop In": "drop-in",
    "Scheduled": "scheduled",
    "In Progress": "in-progress",
    "Completed": "completed",
    "Pending For Quote Approval": "pending",
    "Cancelled": "cancelled",
    "Approved": "completed",
    "Pending": "pending",
    "Draft": "created",
    "Moved to Tech": "moved-to-tech"
};

// Export data for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { workOrdersData, statusColors };
}
